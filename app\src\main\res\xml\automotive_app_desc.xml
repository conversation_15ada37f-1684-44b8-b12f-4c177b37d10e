<?xml version="1.0" encoding="utf-8"?>
<automotiveApp>
    <!-- 应用类型：媒体应用 -->
    <uses name="media" />
    
    <!-- 应用支持的功能 -->
    <uses name="audio_playback" />
    <uses name="media_browsing" />
    
    <!-- 应用元数据 -->
    <metadata>
        <name>轻聆音乐</name>
        <description>专为车载环境设计的智能音乐播放器</description>
        <category>CATEGORY_AUDIO</category>
        <version>1.0</version>
    </metadata>
    
    <!-- 车载专用配置 -->
    <automotive>
        <!-- 支持的屏幕尺寸 -->
        <supports-screens
            android:largeScreens="true"
            android:xlargeScreens="true"
            android:requiresSmallestWidthDp="720" />
            
        <!-- 车载环境优化 -->
        <uses-configuration
            android:reqHardKeyboard="false"
            android:reqKeyboardType="nokeys"
            android:reqNavigation="nonav"
            android:reqTouchScreen="finger" />
    </automotive>
</automotiveApp>
