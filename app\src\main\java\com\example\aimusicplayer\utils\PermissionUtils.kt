package com.example.aimusicplayer.utils

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.appcompat.app.AlertDialog
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

/**
 * 权限工具类，用于处理应用权限请求
 */
object PermissionUtils {
    private const val TAG = "PermissionUtils"

    /**
     * 检查是否有指定权限
     * @param context 上下文
     * @param permissions 需要检查的权限
     * @return 如果所有权限都已授予，返回true；否则返回false
     */
    @JvmStatic
    fun hasPermissions(context: Context?, vararg permissions: String): Boolean {
        if (context != null && permissions.isNotEmpty()) {
            for (permission in permissions) {
                if (ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                    return false
                }
            }
        }
        return true
    }

    /**
     * 获取需要请求的权限列表（尚未授予的权限）
     * @param context 上下文
     * @param permissions 需要检查的权限
     * @return 尚未授予的权限列表
     */
    @JvmStatic
    fun getUngratedPermissions(context: Context?, vararg permissions: String): Array<String> {
        val ungrantedPermissions = ArrayList<String>()

        if (context != null && permissions.isNotEmpty()) {
            for (permission in permissions) {
                if (ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                    ungrantedPermissions.add(permission)
                }
            }
        }

        return ungrantedPermissions.toTypedArray()
    }

    /**
     * 请求所需权限
     * @param activity Activity实例
     * @param requestCode 请求代码
     * @param permissions 需要请求的权限
     */
    @JvmStatic
    fun requestPermissions(activity: Activity, requestCode: Int, vararg permissions: String) {
        val ungrantedPermissions = getUngratedPermissions(activity, *permissions)

        if (ungrantedPermissions.isNotEmpty()) {
            ActivityCompat.requestPermissions(activity, ungrantedPermissions, requestCode)
        }
    }

    /**
     * 检查是否应该显示权限请求的解释
     * @param activity Activity实例
     * @param permissions 需要检查的权限
     * @return 如果至少有一个权限需要显示解释，返回true；否则返回false
     */
    @JvmStatic
    fun shouldShowRequestPermissionRationale(activity: Activity, vararg permissions: String): Boolean {
        for (permission in permissions) {
            if (ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)) {
                return true
            }
        }
        return false
    }

    /**
     * 显示权限解释对话框
     * @param activity Activity实例
     * @param message 显示的消息
     * @param positiveListener 确认按钮点击监听器
     * @param negativeListener 取消按钮点击监听器
     */
    @JvmStatic
    fun showPermissionRationaleDialog(
        activity: Activity,
        message: String,
        positiveListener: DialogInterface.OnClickListener,
        negativeListener: DialogInterface.OnClickListener
    ) {
        AlertDialog.Builder(activity)
            .setTitle("需要权限")
            .setMessage(message)
            .setPositiveButton("授予权限", positiveListener)
            .setNegativeButton("取消", negativeListener)
            .create()
            .show()
    }

    /**
     * 显示权限设置对话框
     * @param activity Activity实例
     * @param message 显示的消息
     */
    @JvmStatic
    fun showPermissionSettingsDialog(activity: Activity, message: String) {
        AlertDialog.Builder(activity)
            .setTitle("需要权限")
            .setMessage(message)
            .setPositiveButton("去设置") { _, _ ->
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                val uri = Uri.fromParts("package", activity.packageName, null)
                intent.data = uri
                activity.startActivity(intent)
            }
            .setNegativeButton("取消") { dialog, _ ->
                dialog.dismiss()
            }
            .create()
            .show()
    }

    /**
     * 获取音乐播放需要的基本权限
     * @return 权限数组
     */
    @JvmStatic
    fun getMusicPlaybackPermissions(): Array<String> {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> {
                arrayOf(
                    Manifest.permission.READ_MEDIA_AUDIO,
                    Manifest.permission.POST_NOTIFICATIONS
                )
            }
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.R -> {
                arrayOf(
                    Manifest.permission.READ_EXTERNAL_STORAGE
                )
            }
            else -> {
                arrayOf(
                    Manifest.permission.READ_EXTERNAL_STORAGE,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                )
            }
        }
    }

    /**
     * 获取语音功能需要的权限
     * @return 权限数组
     */
    @JvmStatic
    fun getVoicePermissions(): Array<String> {
        return arrayOf(
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.ACCESS_NETWORK_STATE,
            Manifest.permission.INTERNET,
            Manifest.permission.READ_PHONE_STATE
        )
    }

    /**
     * 获取Android Automotive专用权限
     * @return 权限数组
     */
    @JvmStatic
    fun getAutomotivePermissions(): Array<String> {
        val permissions = mutableListOf<String>()

        // 基础权限
        permissions.addAll(getMusicPlaybackPermissions())

        // 车载专用权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            permissions.add(Manifest.permission.FOREGROUND_SERVICE)
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            permissions.add(Manifest.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK)
        }

        // 媒体控制权限
        permissions.add(Manifest.permission.MODIFY_AUDIO_SETTINGS)
        permissions.add(Manifest.permission.WAKE_LOCK)

        return permissions.toTypedArray()
    }

    /**
     * 检查是否在Android Automotive环境中运行
     * @param context 上下文
     * @return 是否为车载环境
     */
    @JvmStatic
    fun isAutomotiveEnvironment(context: Context): Boolean {
        return context.packageManager.hasSystemFeature(PackageManager.FEATURE_AUTOMOTIVE)
    }
}
