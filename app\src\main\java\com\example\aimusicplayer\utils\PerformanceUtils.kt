package com.example.aimusicplayer.utils

import android.app.Activity
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference

/**
 * 性能优化工具类
 * 提供各种性能优化相关的工具方法
 * 特别适合车载场景的性能优化
 */
object PerformanceUtils {

    /**
     * 启用硬件加速
     * 对于动画和绘制密集型视图，启用硬件加速可以显著提高性能
     *
     * @param view 需要启用硬件加速的视图
     */
    fun enableHardwareAcceleration(view: View) {
        view.setLayerType(View.LAYER_TYPE_HARDWARE, null)
    }

    /**
     * 禁用硬件加速
     * 对于某些特殊绘制操作（如特定的自定义绘制），可能需要禁用硬件加速
     *
     * @param view 需要禁用硬件加速的视图
     */
    fun disableHardwareAcceleration(view: View) {
        view.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
    }

    /**
     * 设置全屏模式
     * 隐藏状态栏和导航栏，提供沉浸式体验
     * 针对Android Automotive环境优化
     *
     * @param activity 活动
     * @param fullscreen 是否全屏
     */
    @JvmStatic
    fun setFullscreen(activity: Activity, fullscreen: Boolean) {
        val window = activity.window
        val decorView = window.decorView

        // 检查是否为车载环境
        val isAutomotive = activity.packageManager.hasSystemFeature("android.hardware.type.automotive")

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11及以上使用WindowInsetsController
            val controller = ViewCompat.getWindowInsetsController(decorView)
            if (controller != null) {
                if (fullscreen) {
                    // 车载环境下隐藏系统栏
                    controller.hide(WindowInsetsCompat.Type.systemBars())
                    controller.systemBarsBehavior = if (isAutomotive) {
                        // 车载环境使用更严格的隐藏行为
                        WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
                    } else {
                        WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
                    }
                } else {
                    controller.show(WindowInsetsCompat.Type.systemBars())
                }
            }
        } else {
            // 旧版本使用传统方法
            if (fullscreen) {
                @Suppress("DEPRECATION")
                window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
                @Suppress("DEPRECATION")
                decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                        or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                        or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                        or View.SYSTEM_UI_FLAG_FULLSCREEN)
            } else {
                @Suppress("DEPRECATION")
                window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
                @Suppress("DEPRECATION")
                decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_VISIBLE
            }
        }

        // 车载环境专用设置
        if (isAutomotive) {
            setupAutomotiveWindow(window)
        }
    }

    /**
     * 设置Android Automotive专用窗口配置
     * @param window 窗口对象
     */
    private fun setupAutomotiveWindow(window: Window) {
        // 保持屏幕常亮（车载环境重要）
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        // 设置窗口布局参数，适配车载大屏
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            val layoutParams = window.attributes
            layoutParams.layoutInDisplayCutoutMode =
                WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
            window.attributes = layoutParams
        }

        // 设置状态栏和导航栏透明
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.statusBarColor = android.graphics.Color.TRANSPARENT
            window.navigationBarColor = android.graphics.Color.TRANSPARENT
        }
    }

    /**
     * 异步执行任务
     * 将耗时操作移至后台线程，避免阻塞UI线程
     *
     * @param backgroundTask 后台任务
     * @param onComplete 完成回调（在主线程执行）
     */
    fun <T> executeAsync(
        backgroundTask: () -> T,
        onComplete: ((T) -> Unit)? = null
    ) {
        CoroutineScope(Dispatchers.Main).launch {
            val result = withContext(Dispatchers.IO) {
                backgroundTask()
            }
            onComplete?.invoke(result)
        }
    }

    /**
     * 延迟执行任务
     * 在指定延迟后执行任务，避免短时间内重复执行
     *
     * @param delayMillis 延迟时间（毫秒）
     * @param task 要执行的任务
     * @return 任务句柄，可用于取消任务
     */
    fun delayTask(delayMillis: Long, task: () -> Unit): TaskHandle {
        val handler = Handler(Looper.getMainLooper())
        val runnable = Runnable { task() }
        handler.postDelayed(runnable, delayMillis)
        return TaskHandle(handler, runnable)
    }

    /**
     * 任务句柄
     * 用于取消延迟任务
     */
    class TaskHandle(
        private val handler: Handler,
        private val runnable: Runnable
    ) {
        /**
         * 取消任务
         */
        fun cancel() {
            handler.removeCallbacks(runnable)
        }
    }

    /**
     * 将Drawable转换为Bitmap
     *
     * @param drawable 要转换的Drawable
     * @param width 目标宽度，默认为Drawable的固有宽度
     * @param height 目标高度，默认为Drawable的固有高度
     * @return 转换后的Bitmap
     */
    fun drawableToBitmap(
        drawable: Drawable,
        width: Int = drawable.intrinsicWidth,
        height: Int = drawable.intrinsicHeight
    ): Bitmap {
        if (drawable is BitmapDrawable) {
            if (drawable.bitmap != null) {
                return drawable.bitmap
            }
        }

        val bitmap = Bitmap.createBitmap(
            if (width <= 0) 1 else width,
            if (height <= 0) 1 else height,
            Bitmap.Config.ARGB_8888
        )

        val canvas = Canvas(bitmap)
        drawable.setBounds(0, 0, canvas.width, canvas.height)
        drawable.draw(canvas)

        return bitmap
    }

    /**
     * 回收视图层次结构中的所有Bitmap
     * 防止内存泄漏
     *
     * @param view 根视图
     */
    fun recycleBitmaps(view: View) {
        if (view is ViewGroup) {
            for (i in 0 until view.childCount) {
                recycleBitmaps(view.getChildAt(i))
            }
        } else if (view.background is BitmapDrawable) {
            val bitmap = (view.background as BitmapDrawable).bitmap
            view.background = null
            bitmap?.recycle()
        }
    }

    /**
     * 优化窗口性能
     * 设置各种窗口标志以提高性能
     *
     * @param window 窗口
     */
    fun optimizeWindowPerformance(window: Window) {
        // 启用硬件加速
        window.setFlags(
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
        )

        // 保持屏幕常亮（适用于车载场景）
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        // 设置窗口背景为不透明，提高性能
        window.setBackgroundDrawableResource(android.R.color.black)
    }
}
