package com.example.aimusicplayer.data.model

import com.google.gson.annotations.SerializedName

/**
 * 搜索响应数据模型
 */
data class SearchResponse(
    @SerializedName("code")
    val code: Int,
    @SerializedName("result")
    val result: SearchResult?
)

data class SearchResult(
    @SerializedName("songs")
    val songs: List<Song>? = null,
    @SerializedName("songCount")
    val songCount: Int = 0,
    @SerializedName("artists")
    val artists: List<Artist>? = null,
    @SerializedName("artistCount")
    val artistCount: Int = 0,
    @SerializedName("albums")
    val albums: List<Album>? = null,
    @SerializedName("albumCount")
    val albumCount: Int = 0,
    @SerializedName("playlists")
    val playlists: List<PlayList>? = null,
    @SerializedName("playlistCount")
    val playlistCount: Int = 0
)

/**
 * 搜索建议响应数据模型
 */
data class SearchSuggestResponse(
    @SerializedName("code")
    val code: Int,
    @SerializedName("result")
    val result: SearchSuggestResult?
)

data class SearchSuggestResult(
    @SerializedName("songs")
    val songs: List<SuggestItem>? = null,
    @SerializedName("artists")
    val artists: List<SuggestItem>? = null,
    @SerializedName("albums")
    val albums: List<SuggestItem>? = null,
    @SerializedName("playlists")
    val playlists: List<SuggestItem>? = null,
    @SerializedName("order")
    val order: List<String>? = null
)

data class SuggestItem(
    @SerializedName("id")
    val id: Long,
    @SerializedName("name")
    val name: String,
    @SerializedName("artists")
    val artists: List<Artist>? = null,
    @SerializedName("album")
    val album: Album? = null
)
